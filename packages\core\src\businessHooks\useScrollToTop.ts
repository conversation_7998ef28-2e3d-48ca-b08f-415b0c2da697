'use client'

import { useCallback, useEffect } from 'react'
import { usePathname } from 'next/navigation'

interface UseScrollToTopOptions {
  /** 触发方式：'route' = 路由变化时, 'manual' = 手动调用, 'both' = 两者都支持 */
  trigger?: 'route' | 'manual' | 'both'
  /** 滚动行为：'smooth' = 平滑滚动, 'auto' = 立即滚动 */
  behavior?: 'smooth' | 'auto'
  /** 排除的路径，这些路径不会自动滚动到顶部 */
  excludePaths?: string[]
  /** 是否在组件挂载时滚动到顶部 */
  scrollOnMount?: boolean
}

/**
 * 滚动到顶部的自定义Hook
 *
 * @param options 配置选项
 * @returns { scrollToTop } 手动滚动到顶部的函数
 *
 * @example
 * ```tsx
 * // 基础用法 - 路由变化时自动滚动
 * const { scrollToTop } = useScrollToTop()
 *
 * // 只手动控制
 * const { scrollToTop } = useScrollToTop({ trigger: 'manual' })
 *
 * // 排除特定页面
 * const { scrollToTop } = useScrollToTop({
 *   excludePaths: ['/checkout/paying', '/customer/account']
 * })
 * ```
 */
const useScrollToTop = (options: UseScrollToTopOptions = {}) => {
  const pathname = usePathname()

  const {
    trigger = 'both',
    behavior = 'smooth',
    excludePaths = [
      // '/checkout/paying',
    ],
    scrollOnMount = false,
  } = options

  /**
   * 检查当前路径是否应该被排除
   */
  const shouldExclude = useCallback(() => {
    return excludePaths.some((path) => pathname.includes(path))
  }, [pathname, excludePaths])

  /**
   * 执行滚动到顶部
   */
  const performScroll = useCallback(
    (scrollBehavior: 'smooth' | 'auto' = behavior) => {
      if (typeof window !== 'undefined') {
        window.scrollTo({
          top: 0,
          behavior: scrollBehavior,
        })
      }
    },
    [behavior],
  )

  /**
   * 手动滚动到顶部的函数
   */
  const scrollToTop = useCallback(
    (customBehavior?: 'smooth' | 'auto') => {
      if (trigger === 'manual' || trigger === 'both') {
        performScroll(customBehavior)
      }
    },
    [trigger, performScroll],
  )

  /**
   * 路由变化时自动滚动到顶部
   */
  useEffect(() => {
    if (shouldExclude()) return

    if (trigger === 'route' || trigger === 'both') {
      // 路由变化时使用auto行为，避免不必要的动画
      performScroll('auto')
    }
  }, [pathname, trigger, shouldExclude, performScroll])

  /**
   * 组件挂载时滚动到顶部（用于页面刷新场景）
   */
  useEffect(() => {
    if (scrollOnMount && !shouldExclude()) {
      performScroll('auto')
    }
  }, [scrollOnMount, shouldExclude, performScroll])

  return {
    scrollToTop,
    /** 当前路径是否被排除 */
    isExcluded: shouldExclude(),
    /** 当前路径名 */
    pathname,
  }
}

// 命名导出
export { useScrollToTop }

// 默认导出
export default useScrollToTop
